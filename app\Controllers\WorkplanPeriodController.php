<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\WorkplanPeriodModel;
use App\Models\UserModel;
use App\Models\PerformanceOutputsModel;
use App\Models\DutyInstructionItemsModel;
use App\Models\WorkplanActivityModel;
use App\Models\OutputDutyInstructionModel;
use App\Models\OutputWorkplanActivitiesModel;

/**
 * WorkplanPeriodController
 *
 * Handles CRUD operations for workplan periods
 */
class WorkplanPeriodController extends BaseController
{
    protected $workplanPeriodModel;
    protected $userModel;
    protected $performanceOutputsModel;
    protected $dutyInstructionItemsModel;
    protected $workplanActivityModel;
    protected $outputDutyInstructionModel;
    protected $outputWorkplanActivitiesModel;
    protected $helpers = ['form', 'url'];

    public function __construct()
    {
        $this->workplanPeriodModel = new WorkplanPeriodModel();
        $this->userModel = new UserModel();
        $this->performanceOutputsModel = new PerformanceOutputsModel();
        $this->dutyInstructionItemsModel = new DutyInstructionItemsModel();
        $this->workplanActivityModel = new WorkplanActivityModel();
        $this->outputDutyInstructionModel = new OutputDutyInstructionModel();
        $this->outputWorkplanActivitiesModel = new OutputWorkplanActivitiesModel();
    }

    /**
     * Display list of workplan periods
     */
    public function index()
    {
        $data = [
            'title' => 'Workplan Periods',
            'workplan_periods' => $this->workplanPeriodModel->getAllWithDetails()
        ];

        return view('workplan_period/workplan_period_index', $data);
    }

    /**
     * Show form for creating new workplan period
     */
    public function new()
    {
        $data = [
            'title' => 'Create Workplan Period',
            'users' => $this->userModel->findAll()
        ];

        return view('workplan_period/workplan_period_create', $data);
    }

    /**
     * Create new workplan period
     */
    public function create()
    {
        $rules = [
            'user_id' => 'required|integer',
            'title' => 'required|max_length[255]',
            'description' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'user_id' => $this->request->getPost('user_id'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'created_by' => session()->get('user_id')
        ];

        // Handle file upload
        $file = $this->request->getFile('workplan_period_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/workplan_periods', $newName);
            $data['workplan_period_filepath'] = 'public/uploads/workplan_periods/' . $newName;
        }

        if ($this->workplanPeriodModel->insert($data)) {
            return redirect()->to(base_url('workplan-period'))->with('success', 'Workplan period created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create workplan period.');
        }
    }

    /**
     * Show workplan period details
     */
    public function show($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->getWorkplanPeriodWithDetails($id);
        
        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        $data = [
            'title' => 'Workplan Period Details',
            'workplan_period' => $workplanPeriod
        ];

        return view('workplan_period/workplan_period_show', $data);
    }

    /**
     * Show form for editing workplan period
     */
    public function edit($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($id);
        
        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        $data = [
            'title' => 'Edit Workplan Period',
            'workplan_period' => $workplanPeriod,
            'users' => $this->userModel->findAll()
        ];

        return view('workplan_period/workplan_period_edit', $data);
    }

    /**
     * Update workplan period
     */
    public function update($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($id);
        
        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        $rules = [
            'user_id' => 'required|integer',
            'title' => 'required|max_length[255]',
            'description' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'user_id' => $this->request->getPost('user_id'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'updated_by' => session()->get('user_id')
        ];

        // Handle file upload
        $file = $this->request->getFile('workplan_period_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Delete old file if exists
            if (isset($workplanPeriod['workplan_period_filepath']) && $workplanPeriod['workplan_period_filepath'] && file_exists(ROOTPATH . $workplanPeriod['workplan_period_filepath'])) {
                unlink(ROOTPATH . $workplanPeriod['workplan_period_filepath']);
            }
            
            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/workplan_periods', $newName);
            $data['workplan_period_filepath'] = 'public/uploads/workplan_periods/' . $newName;
        }

        if ($this->workplanPeriodModel->update($id, $data)) {
            return redirect()->to(base_url('workplan-period'))->with('success', 'Workplan period updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update workplan period.');
        }
    }

    /**
     * Delete workplan period
     */
    public function delete($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($id);
        
        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        $data = [
            'deleted_by' => session()->get('user_id')
        ];

        if ($this->workplanPeriodModel->update($id, $data) && $this->workplanPeriodModel->delete($id)) {
            return redirect()->to(base_url('workplan-period'))->with('success', 'Workplan period deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete workplan period.');
        }
    }

    /**
     * Update workplan period status
     */
    public function updateStatus($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($id);
        
        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        $status = $this->request->getPost('status');
        $remarks = $this->request->getPost('remarks');

        if ($this->workplanPeriodModel->updateStatus($id, $status, $remarks)) {
            return redirect()->back()->with('success', 'Workplan period status updated successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to update workplan period status.');
        }
    }

    /**
     * View outputs for a workplan period
     */
    public function viewOutputs($id)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($id);

        if (!$workplanPeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period not found');
        }

        // Get outputs for this workplan period
        $outputs = $this->performanceOutputsModel->getByPerformancePeriod($id);

        // Add link status information for each output
        foreach ($outputs as &$output) {
            // Check for duty instruction links
            $dutyInstructionLinks = $this->outputDutyInstructionModel->getDutyInstructionsByOutput($output['id']);
            $output['has_duty_instruction_links'] = !empty($dutyInstructionLinks);
            $output['duty_instruction_count'] = count($dutyInstructionLinks);

            // Check for workplan activity links
            $workplanActivityLinks = $this->outputWorkplanActivitiesModel->getWorkplanActivitiesByOutput($output['id']);
            $output['has_workplan_activity_links'] = !empty($workplanActivityLinks);
            $output['workplan_activity_count'] = count($workplanActivityLinks);
        }

        $data = [
            'title' => 'Outputs for: ' . $workplanPeriod['title'],
            'workplan_period' => $workplanPeriod,
            'outputs' => $outputs
        ];

        return view('workplan_period/workplan_period_outputs', $data);
    }

    /**
     * Manage output links to duty instructions and workplan activities
     */
    public function manageOutputLinks($workplanPeriodId, $outputId)
    {
        $workplanPeriod = $this->workplanPeriodModel->find($workplanPeriodId);
        $output = $this->performanceOutputsModel->find($outputId);

        if (!$workplanPeriod || !$output) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Workplan period or output not found');
        }

        // Get duty instruction items with their parent duty instruction details
        $dutyInstructionItems = $this->dutyInstructionItemsModel->getAllWithDetails();

        // Get workplan activities
        $workplanActivities = $this->workplanActivityModel->getActivitiesWithDetails();

        // Get existing links
        $existingDutyInstructionLinks = $this->outputDutyInstructionModel->getDutyInstructionsByOutput($outputId);
        $existingWorkplanActivityLinks = $this->outputWorkplanActivitiesModel->getWorkplanActivitiesByOutput($outputId);

        $data = [
            'title' => 'Manage Links for: ' . $output['output'],
            'workplan_period' => $workplanPeriod,
            'output' => $output,
            'duty_instruction_items' => $dutyInstructionItems,
            'workplan_activities' => $workplanActivities,
            'existing_duty_instruction_links' => $existingDutyInstructionLinks,
            'existing_workplan_activity_links' => $existingWorkplanActivityLinks
        ];

        return view('workplan_period/workplan_period_outputs_links', $data);
    }

    /**
     * Link output to duty instruction item
     */
    public function linkToDutyInstruction($workplanPeriodId, $outputId)
    {
        $rules = [
            'duty_instruction_item_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $dutyInstructionItemId = $this->request->getPost('duty_instruction_item_id');

        // Check if link already exists
        if ($this->outputDutyInstructionModel->isLinked($outputId, $dutyInstructionItemId)) {
            return redirect()->back()->with('error', 'This output is already linked to the selected duty instruction item.');
        }

        if ($this->outputDutyInstructionModel->linkOutputToDutyInstruction($outputId, $dutyInstructionItemId)) {
            return redirect()->back()->with('success', 'Output successfully linked to duty instruction item.');
        } else {
            return redirect()->back()->with('error', 'Failed to link output to duty instruction item.');
        }
    }

    /**
     * Link output to workplan activity
     */
    public function linkToWorkplanActivity($workplanPeriodId, $outputId)
    {
        $rules = [
            'workplan_activity_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $workplanActivityId = $this->request->getPost('workplan_activity_id');

        // Check if link already exists
        if ($this->outputWorkplanActivitiesModel->isLinked($outputId, $workplanActivityId)) {
            return redirect()->back()->with('error', 'This output is already linked to the selected workplan activity.');
        }

        if ($this->outputWorkplanActivitiesModel->linkOutputToActivity($outputId, $workplanActivityId)) {
            return redirect()->back()->with('success', 'Output successfully linked to workplan activity.');
        } else {
            return redirect()->back()->with('error', 'Failed to link output to workplan activity.');
        }
    }

    /**
     * Remove duty instruction link
     */
    public function removeDutyInstructionLink($workplanPeriodId, $outputId, $linkId)
    {
        // Find the specific link record
        $link = $this->outputDutyInstructionModel->find($linkId);

        if (!$link || $link['output_id'] != $outputId) {
            return redirect()->back()->with('error', 'Link not found or invalid.');
        }

        if ($this->outputDutyInstructionModel->delete($linkId)) {
            return redirect()->back()->with('success', 'Duty instruction link removed successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to remove duty instruction link.');
        }
    }

    /**
     * Remove workplan activity link
     */
    public function removeWorkplanActivityLink($workplanPeriodId, $outputId, $linkId)
    {
        // Find the specific link record
        $link = $this->outputWorkplanActivitiesModel->find($linkId);

        if (!$link || $link['output_id'] != $outputId) {
            return redirect()->back()->with('error', 'Link not found or invalid.');
        }

        if ($this->outputWorkplanActivitiesModel->delete($linkId)) {
            return redirect()->back()->with('success', 'Workplan activity link removed successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to remove workplan activity link.');
        }
    }
}
