<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><?= $title ?></h5>
            <div class="btn-group">
                <a href="<?= base_url('workplan-period') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
                <a href="<?= base_url('workplan-period/' . $workplan_period['id']) ?>" class="btn btn-outline-primary">
                    <i class="fas fa-eye"></i> View Details
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Workplan Period Info -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Workplan Period Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Title:</strong> <?= esc($workplan_period['title']) ?></p>
                                    <p><strong>Status:</strong> 
                                        <?php
                                        $statusClass = match($workplan_period['status']) {
                                            'pending' => 'bg-warning text-dark',
                                            'approved' => 'bg-success',
                                            'rejected' => 'bg-danger',
                                            'submitted' => 'bg-info',
                                            default => 'bg-secondary'
                                        };
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= ucfirst(esc($workplan_period['status'])) ?></span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Created:</strong> <?= date('M d, Y', strtotime($workplan_period['created_at'])) ?></p>
                                    <?php if ($workplan_period['description']): ?>
                                        <p><strong>Description:</strong> <?= esc($workplan_period['description']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Outputs Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Output</th>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Unit of Measurement</th>
                            <th>Status</th>
                            <th>Links</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($outputs)): ?>
                            <?php foreach ($outputs as $output): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($output['output']) ?></strong>
                                    </td>
                                    <td>
                                        <?= $output['description'] ? esc($output['description']) : '<em class="text-muted">No description</em>' ?>
                                    </td>
                                    <td><?= esc($output['quantity']) ?></td>
                                    <td><?= esc($output['unit_of_measurement']) ?></td>
                                    <td>
                                        <?php
                                        $statusClass = match($output['status']) {
                                            'active' => 'bg-success',
                                            'pending' => 'bg-warning text-dark',
                                            'completed' => 'bg-primary',
                                            'cancelled' => 'bg-danger',
                                            default => 'bg-secondary'
                                        };
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= ucfirst(esc($output['status'])) ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <!-- Duty Instruction Links Status -->
                                            <div class="me-3" title="Duty Instruction Links">
                                                <?php if ($output['has_duty_instruction_links']): ?>
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    <small class="text-success"><?= $output['duty_instruction_count'] ?> DI</small>
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle text-muted me-1"></i>
                                                    <small class="text-muted">0 DI</small>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Workplan Activity Links Status -->
                                            <div title="Workplan Activity Links">
                                                <?php if ($output['has_workplan_activity_links']): ?>
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    <small class="text-success"><?= $output['workplan_activity_count'] ?> WA</small>
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle text-muted me-1"></i>
                                                    <small class="text-muted">0 WA</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= date('M d, Y', strtotime($output['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('performance-outputs/' . $output['id']) ?>" class="btn btn-outline-primary" title="View Details" style="margin-right: 5px;">
                                                <i class="fas fa-eye me-1"></i> View
                                            </a>
                                            <a href="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs/' . $output['id'] . '/links') ?>" class="btn btn-outline-info" title="Manage Links" style="margin-right: 5px;">
                                                <i class="fas fa-link me-1"></i> Links
                                            </a>
                                            <a href="<?= base_url('performance-outputs/' . $output['id'] . '/edit') ?>" class="btn btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit me-1"></i> Edit
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="py-4">
                                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No outputs found for this workplan period.</p>
                                        <a href="<?= base_url('performance-outputs/new') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Create First Output
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Links Legend -->
            <?php if (!empty($outputs)): ?>
                <div class="mt-3">
                    <div class="card border-0 bg-light">
                        <div class="card-body py-2">
                            <small class="text-muted">
                                <strong>Links Legend:</strong>
                                <span class="ms-2">
                                    <i class="fas fa-check-circle text-success"></i> Linked
                                    <span class="ms-2">
                                        <i class="fas fa-times-circle text-muted"></i> Not Linked
                                    </span>
                                    <span class="ms-3">
                                        <strong>DI:</strong> Duty Instructions
                                        <span class="ms-2">
                                            <strong>WA:</strong> Workplan Activities
                                        </span>
                                    </span>
                                </span>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Summary Statistics -->
            <?php if (!empty($outputs)): ?>
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Summary</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-primary"><?= count($outputs) ?></h4>
                                            <p class="mb-0">Total Outputs</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-success"><?= count(array_filter($outputs, fn($o) => $o['status'] === 'active')) ?></h4>
                                            <p class="mb-0">Active</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-warning"><?= count(array_filter($outputs, fn($o) => $o['status'] === 'pending')) ?></h4>
                                            <p class="mb-0">Pending</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-info"><?= count(array_filter($outputs, fn($o) => $o['status'] === 'completed')) ?></h4>
                                            <p class="mb-0">Completed</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
