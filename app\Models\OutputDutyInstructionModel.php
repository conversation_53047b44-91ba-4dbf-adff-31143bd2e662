<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * OutputDutyInstructionModel
 *
 * Handles database operations for the output_duty_instruction junction table
 * which links performance outputs to duty instructions
 */
class OutputDutyInstructionModel extends Model
{
    protected $table            = 'output_duty_instruction';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    // Fields that can be set during save/insert/update
    protected $allowedFields    = [
        'output_id',
        'duty_instruction_item_id',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'output_id'                 => 'required|integer',
        'duty_instruction_item_id'  => 'required|integer',
        'created_by'                => 'permit_empty|integer',
        'updated_by'                => 'permit_empty|integer',
        'deleted_by'                => 'permit_empty|integer'
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    /**
     * Get all duty instruction items for a specific output
     *
     * @param int $outputId
     * @return array
     */
    public function getDutyInstructionsByOutput($outputId)
    {
        return $this->select('output_duty_instruction.*, duty_instruction_items.instruction, duty_instruction_items.instruction_number, duty_instructions.duty_instruction_title, duty_instructions.duty_instruction_number')
                    ->join('duty_instruction_items', 'duty_instruction_items.id = output_duty_instruction.duty_instruction_item_id', 'left')
                    ->join('duty_instructions', 'duty_instructions.id = duty_instruction_items.duty_instruction_id', 'left')
                    ->where('output_duty_instruction.output_id', $outputId)
                    ->findAll();
    }

    /**
     * Get all outputs for a specific duty instruction item
     *
     * @param int $dutyInstructionItemId
     * @return array
     */
    public function getOutputsByDutyInstructionItem($dutyInstructionItemId)
    {
        return $this->select('output_duty_instruction.*, performance_outputs.output, performance_outputs.description')
                    ->join('performance_outputs', 'performance_outputs.id = output_duty_instruction.output_id', 'left')
                    ->where('output_duty_instruction.duty_instruction_item_id', $dutyInstructionItemId)
                    ->findAll();
    }

    /**
     * Link an output to a duty instruction item
     *
     * @param int $outputId
     * @param int $dutyInstructionItemId
     * @return bool
     */
    public function linkOutputToDutyInstruction($outputId, $dutyInstructionItemId)
    {
        $data = [
            'output_id' => $outputId,
            'duty_instruction_item_id' => $dutyInstructionItemId,
            'created_by' => session()->get('user_id') ?? null
        ];

        return $this->insert($data);
    }

    /**
     * Unlink an output from a duty instruction item
     *
     * @param int $outputId
     * @param int $dutyInstructionItemId
     * @return bool
     */
    public function unlinkOutputFromDutyInstruction($outputId, $dutyInstructionItemId)
    {
        return $this->where('output_id', $outputId)
                    ->where('duty_instruction_item_id', $dutyInstructionItemId)
                    ->delete();
    }

    /**
     * Check if an output is linked to a duty instruction item
     *
     * @param int $outputId
     * @param int $dutyInstructionItemId
     * @return bool
     */
    public function isLinked($outputId, $dutyInstructionItemId)
    {
        $result = $this->where('output_id', $outputId)
                       ->where('duty_instruction_item_id', $dutyInstructionItemId)
                       ->first();

        return !empty($result);
    }
}
