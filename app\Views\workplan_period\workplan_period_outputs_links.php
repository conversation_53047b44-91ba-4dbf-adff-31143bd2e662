<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><?= $title ?></h5>
            <div class="btn-group">
                <a href="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Outputs
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Output Information -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Output Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Output:</strong> <?= esc($output['output']) ?></p>
                                    <p><strong>Quantity:</strong> <?= esc($output['quantity']) ?> <?= esc($output['unit_of_measurement']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Status:</strong> 
                                        <?php
                                        $statusClass = match($output['status']) {
                                            'active' => 'bg-success',
                                            'pending' => 'bg-warning text-dark',
                                            'completed' => 'bg-primary',
                                            'cancelled' => 'bg-danger',
                                            default => 'bg-secondary'
                                        };
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= ucfirst(esc($output['status'])) ?></span>
                                    </p>
                                    <?php if ($output['description']): ?>
                                        <p><strong>Description:</strong> <?= esc($output['description']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Form 1: Link to Duty Instruction Items -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Link to Duty Instruction Items
                            </h6>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs/' . $output['id'] . '/links/duty-instruction') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <div class="mb-3">
                                    <label for="duty_instruction_item_id" class="form-label">Select Duty Instruction Item <span class="text-danger">*</span></label>
                                    <select class="form-select duty-instruction-select" id="duty_instruction_item_id" name="duty_instruction_item_id" required>
                                        <option value="">Search and select duty instruction item...</option>
                                        <?php foreach ($duty_instruction_items as $item): ?>
                                            <option value="<?= $item['id'] ?>" data-item-text="<?= esc($item['instruction']) ?>" data-instruction-title="<?= esc($item['duty_instruction_title']) ?>">
                                                <?= esc($item['instruction']) ?> - <?= esc($item['duty_instruction_title']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-link me-1"></i> Link to Duty Instruction
                                </button>
                            </form>

                            <!-- Existing Duty Instruction Links -->
                            <?php if (!empty($existing_duty_instruction_links)): ?>
                                <hr>
                                <h6>Existing Links:</h6>
                                <div class="list-group">
                                    <?php foreach ($existing_duty_instruction_links as $link): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?= esc($link['instruction']) ?></strong>
                                                <br><small class="text-muted"><?= esc($link['duty_instruction_title']) ?></small>
                                                <?php if (!empty($link['instruction_number'])): ?>
                                                    <br><small class="text-muted">Item #: <?= esc($link['instruction_number']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-success me-2">Linked</span>
                                                <form action="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs/' . $output['id'] . '/links/duty-instruction/' . $link['id'] . '/remove') ?>" method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to remove this link?')">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Remove Link">
                                                        <i class="fas fa-unlink"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <hr>
                                <p class="text-muted">No duty instruction links found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Form 2: Link to Workplan Activities -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>Link to Workplan Activities
                            </h6>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs/' . $output['id'] . '/links/workplan-activity') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <div class="mb-3">
                                    <label for="workplan_activity_id" class="form-label">Select Workplan Activity <span class="text-danger">*</span></label>
                                    <select class="form-select" id="workplan_activity_id" name="workplan_activity_id" required>
                                        <option value="">Select workplan activity...</option>
                                        <?php foreach ($workplan_activities as $activity): ?>
                                            <option value="<?= $activity['id'] ?>">
                                                [<?= esc($activity['activity_code']) ?>] <?= esc($activity['title']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-link me-1"></i> Link to Workplan Activity
                                </button>
                            </form>

                            <!-- Existing Workplan Activity Links -->
                            <?php if (!empty($existing_workplan_activity_links)): ?>
                                <hr>
                                <h6>Existing Links:</h6>
                                <div class="list-group">
                                    <?php foreach ($existing_workplan_activity_links as $link): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>[<?= esc($link['activity_code']) ?>] <?= esc($link['activity_title']) ?></strong>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-success me-2">Linked</span>
                                                <form action="<?= base_url('workplan-period/' . $workplan_period['id'] . '/outputs/' . $output['id'] . '/links/workplan-activity/' . $link['id'] . '/remove') ?>" method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to remove this link?')">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Remove Link">
                                                        <i class="fas fa-unlink"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <hr>
                                <p class="text-muted">No workplan activity links found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 for duty instruction items with search functionality
    $('.duty-instruction-select').select2({
        placeholder: 'Search and select duty instruction item...',
        allowClear: true,
        width: '100%',
        templateResult: function(option) {
            if (!option.id) {
                return option.text;
            }
            
            var $option = $(option.element);
            var itemText = $option.data('item-text');
            var instructionTitle = $option.data('instruction-title');
            
            if (itemText && instructionTitle) {
                return $('<div><strong>' + itemText + '</strong><br><small class="text-muted">' + instructionTitle + '</small></div>');
            }
            
            return option.text;
        },
        templateSelection: function(option) {
            if (!option.id) {
                return option.text;
            }
            
            var $option = $(option.element);
            var itemText = $option.data('item-text');
            var instructionTitle = $option.data('instruction-title');
            
            if (itemText && instructionTitle) {
                return itemText + ' - ' + instructionTitle;
            }
            
            return option.text;
        }
    });
});
</script>

<?= $this->endSection() ?>
