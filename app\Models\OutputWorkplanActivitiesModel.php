<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * OutputWorkplanActivitiesModel
 *
 * Handles database operations for the output_workplan_activities junction table
 * which links performance outputs to workplan activities
 */
class OutputWorkplanActivitiesModel extends Model
{
    protected $table            = 'output_workplan_activities';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    // Fields that can be set during save/insert/update
    protected $allowedFields    = [
        'output_id',
        'workplan_activity_id',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'output_id'             => 'required|integer',
        'workplan_activity_id'  => 'required|integer',
        'created_by'            => 'permit_empty|integer',
        'updated_by'            => 'permit_empty|integer',
        'deleted_by'            => 'permit_empty|integer'
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    /**
     * Get all workplan activities for a specific output
     *
     * @param int $outputId
     * @return array
     */
    public function getWorkplanActivitiesByOutput($outputId)
    {
        return $this->select('output_workplan_activities.*, workplan_activities.title as activity_title, workplan_activities.activity_code')
                    ->join('workplan_activities', 'workplan_activities.id = output_workplan_activities.workplan_activity_id', 'left')
                    ->where('output_workplan_activities.output_id', $outputId)
                    ->findAll();
    }

    /**
     * Get all outputs for a specific workplan activity
     *
     * @param int $workplanActivityId
     * @return array
     */
    public function getOutputsByWorkplanActivity($workplanActivityId)
    {
        return $this->select('output_workplan_activities.*, performance_outputs.output, performance_outputs.description')
                    ->join('performance_outputs', 'performance_outputs.id = output_workplan_activities.output_id', 'left')
                    ->where('output_workplan_activities.workplan_activity_id', $workplanActivityId)
                    ->findAll();
    }

    /**
     * Link an output to a workplan activity
     *
     * @param int $outputId
     * @param int $workplanActivityId
     * @return bool
     */
    public function linkOutputToActivity($outputId, $workplanActivityId)
    {
        $data = [
            'output_id' => $outputId,
            'workplan_activity_id' => $workplanActivityId,
            'created_by' => session()->get('user_id') ?? null
        ];

        return $this->insert($data);
    }

    /**
     * Unlink an output from a workplan activity
     *
     * @param int $outputId
     * @param int $workplanActivityId
     * @return bool
     */
    public function unlinkOutputFromActivity($outputId, $workplanActivityId)
    {
        return $this->where('output_id', $outputId)
                    ->where('workplan_activity_id', $workplanActivityId)
                    ->delete();
    }

    /**
     * Check if an output is linked to a workplan activity
     *
     * @param int $outputId
     * @param int $workplanActivityId
     * @return bool
     */
    public function isLinked($outputId, $workplanActivityId)
    {
        $result = $this->where('output_id', $outputId)
                       ->where('workplan_activity_id', $workplanActivityId)
                       ->first();

        return !empty($result);
    }
}
